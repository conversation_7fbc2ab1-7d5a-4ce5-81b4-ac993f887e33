## Instructions
I need to implement internationalized error messages for the **financialsheet** package model, controller, service, and repository layer. Deprecate the Message constant strings in `internal/errors/types.go` and replace them with literal strings in the error creation calls throughout the financialsheet repository implementation. Please follow these specific steps:

1. **Analyze the source file**:
    - Examine `internal/model/financialsheet/*.go` and identify all instances where `errors.New()` is called to create financialsheet-facing errors. 
    - Examine `internal/controller/financialsheet/*` and identify all instances where `errors.New()` is called to create financialsheet-facing errors.
    - Examine `internal/service/financialsheet/*` and identify all instances where `errors.New()` is called to create financialsheet-facing errors.
    - Examine `internal/repository/financialsheet/*` and identify all instances where `errors.New()` is called to create financialsheet-facing errors.

2. **Create translation keys**: For each financialsheet-facing error found, create a corresponding translation key in `internal/errors/translation_keys.go` following the dot notation pattern `KeyFinancialSheetError[Specific]` = `"financialsheet.error.[specific]"` (e.g., `KeyUserErrorConflict = "user.error.conflict"`, `KeyProgressionErrorInvalid = "progression.error.invalid"`, `KeyFinancialDnaErrorNotFound = "financialdna.error.not_found"`).

3. **Update error creation**: Replace the `errors.New()` calls in the source file to use the new translation keys instead of hardcoded error messages.

4. **Replace Message constants with literal strings**: Example: In `internal/repository/financialsheet/mongo.go`, find all error creation calls that use Message constants (like `errors.FinancialSheetConflictExists`, `errors.FinancialSheetCreateFailed`, etc.) and replace them with their corresponding literal string values.

5. **Use the exact string values**: Replace each Message constant with its exact string value from `internal/errors/types.go`. For example:
   - `errors.FinancialSheetConflictExists` → `"financialsheet already exists"`
   - `errors.FinancialSheetCreateFailed` → `"failed to create financialsheet"`
   - `errors.FinancialSheetByIDNotFound` → `"financialsheet by ID not found"`

6. **Add translations**: Update all three locale files with financialsheet-friendly messages:
   - `locales/pt.json` - Portuguese (PT-BR) messages
   - `locales/en.json` - English messages  
   - `locales/es.json` - Spanish messages

**Requirements**:
- All financialsheet-facing error messages must be friendly and appropriate for a gaming/educational app similar to Duolingo.
- Use the existing error helper functions (`NewValidationError`, `NewNotFoundError`, etc.) where appropriate.
- Follow the established dot notation pattern for translation keys: `financialsheet.error.[specific_error]`.
- Ensure consistency across all three language files.
- Internal errors should NOT expose technical details to users but should still have friendly, generic messages like "Oops! Something went wrong. Please try again."

**Example transformation**:
```go
// Before:
if mongo.IsDuplicateKeyError(err) {
    return "", errors.New(errors.Repository, errors.FinancialSheetConflictExists, errors.Conflict, err)
}

// After: 
if mongo.IsDuplicateKeyError(err) {
    return "", errors.New(errors.Repository, "financialsheet already exists", errors.Conflict, err)
    // And add KeyFinancialSheetErrorConflict = "financialsheet.error.conflict" to translation_keys.go
}
```

**Translation examples for different packages**:

**User Package:**
- `pt.json`: `{"user.error.conflict": "Este usuário já existe. Tente com um email diferente!"}`
- `en.json`: `{"user.error.conflict": "This user already exists. Try with a different email!"}`
- `es.json`: `{"user.error.conflict": "Este usuario ya existe. ¡Intenta con un email diferente!"}`

**Progression Package:**
- `pt.json`: `{"progression.error.invalid": "Progresso inválido. Verifique seus dados!"}`
- `en.json`: `{"progression.error.invalid": "Invalid progression. Please check your data!"}`
- `es.json`: `{"progression.error.invalid": "Progresión inválida. ¡Verifica tus datos!"}`

**FinancialDNA Package:**
- `pt.json`: `{"financialdna.error.not_found": "Perfil financeiro não encontrado!"}`
- `en.json`: `{"financialdna.error.not_found": "Financial profile not found!"}`
- `es.json`: `{"financialdna.error.not_found": "¡Perfil financiero no encontrado!"}`

If you need additional context about the error handling architecture, refer to the `docs/architecture/errors/` folder.

**Scope**: Update financialsheet model, controller, service, and repository error handling, create translation keys, add locale files, and deprecate financialsheet-related Message constants from types.go.

**Verification**: All financialsheet model, controller, service, and repository errors use literal strings, translation keys exist for each error, locale files contain financialsheet-friendly messages, and financialsheet Message constants are removed.

---

**Usage**: Replace `financialsheet` with the specific package you're working on (e.g., `user`, `progression`, `financialdna`) and `FinancialSheet` with the PascalCase version (e.g., `User`, `Progression`, `FinancialDna`), and `financialsheet` with the lowercase version for translation keys and messages.