## Instructions
I need to implement internationalized error messages for the **[PACKAGE_NAME]** package model, controller, service, and repository layer. Deprecate the Message constant strings in `internal/errors/types.go` and replace them with literal strings in the error creation calls throughout the [PACKAGE_NAME] repository implementation. Please follow these specific steps:

1. **Analyze the source file**:
    - Examine `internal/model/[PACKAGE_NAME]/*.go` and identify all instances where `errors.New()` is called to create [PACKAGE_NAME]-facing errors. 
    - Examine `internal/controller/[PACKAGE_NAME]/*` and identify all instances where `errors.New()` is called to create [PACKAGE_NAME]-facing errors.
    - Examine `internal/service/[PACKAGE_NAME]/*` and identify all instances where `errors.New()` is called to create [PACKAGE_NAME]-facing errors.
    - Examine `internal/repository/[PACKAGE_NAME]/*` and identify all instances where `errors.New()` is called to create [PACKAGE_NAME]-facing errors.

2. **Create translation keys**: For each [PACKAGE_NAME]-facing error found, create a corresponding translation key in `internal/errors/translation_keys.go` following the dot notation pattern `Key[PackageName]Error[Specific]` = `"[package_name].error.[specific]"` (e.g., `KeyUserErrorConflict = "user.error.conflict"`, `KeyProgressionErrorInvalid = "progression.error.invalid"`, `KeyFinancialDnaErrorNotFound = "financialdna.error.not_found"`).

3. **Update error creation**: Replace the `errors.New()` calls in the source file to use the new translation keys instead of hardcoded error messages.

4. **Replace Message constants with literal strings**: Example: In `internal/repository/[PACKAGE_NAME]/mongo.go`, find all error creation calls that use Message constants (like `errors.[PackageName]ConflictExists`, `errors.[PackageName]CreateFailed`, etc.) and replace them with their corresponding literal string values.

5. **Use the exact string values**: Replace each Message constant with its exact string value from `internal/errors/types.go`. For example:
   - `errors.[PackageName]ConflictExists` → `"[package_name] already exists"`
   - `errors.[PackageName]CreateFailed` → `"failed to create [package_name]"`
   - `errors.[PackageName]ByIDNotFound` → `"[package_name] by ID not found"`

6. **Add translations**: Update all three locale files with [PACKAGE_NAME]-friendly messages:
   - `locales/pt.json` - Portuguese (PT-BR) messages
   - `locales/en.json` - English messages  
   - `locales/es.json` - Spanish messages

**Requirements**:
- All [PACKAGE_NAME]-facing error messages must be friendly and appropriate for a gaming/educational app similar to Duolingo.
- Use the existing error helper functions (`NewValidationError`, `NewNotFoundError`, etc.) where appropriate.
- Follow the established dot notation pattern for translation keys: `[package_name].error.[specific_error]`.
- Ensure consistency across all three language files.
- Internal errors should NOT expose technical details to users but should still have friendly, generic messages like "Oops! Something went wrong. Please try again."

**Example transformation**:
```go
// Before:
if mongo.IsDuplicateKeyError(err) {
    return "", errors.New(errors.Repository, errors.[PackageName]ConflictExists, errors.Conflict, err)
}

// After: 
if mongo.IsDuplicateKeyError(err) {
    return "", errors.New(errors.Repository, "[package_name] already exists", errors.Conflict, err)
    // And add Key[PackageName]ErrorConflict = "[package_name].error.conflict" to translation_keys.go
}
```

**Translation examples for different packages**:

**User Package:**
- `pt.json`: `{"user.error.conflict": "Este usuário já existe. Tente com um email diferente!"}`
- `en.json`: `{"user.error.conflict": "This user already exists. Try with a different email!"}`
- `es.json`: `{"user.error.conflict": "Este usuario ya existe. ¡Intenta con un email diferente!"}`

**Progression Package:**
- `pt.json`: `{"progression.error.invalid": "Progresso inválido. Verifique seus dados!"}`
- `en.json`: `{"progression.error.invalid": "Invalid progression. Please check your data!"}`
- `es.json`: `{"progression.error.invalid": "Progresión inválida. ¡Verifica tus datos!"}`

**FinancialDNA Package:**
- `pt.json`: `{"financialdna.error.not_found": "Perfil financeiro não encontrado!"}`
- `en.json`: `{"financialdna.error.not_found": "Financial profile not found!"}`
- `es.json`: `{"financialdna.error.not_found": "¡Perfil financiero no encontrado!"}`

If you need additional context about the error handling architecture, refer to the `docs/architecture/errors/` folder.

**Scope**: Update [PACKAGE_NAME] model, controller, service, and repository error handling, create translation keys, add locale files, and deprecate [PACKAGE_NAME]-related Message constants from types.go.

**Verification**: All [PACKAGE_NAME] model, controller, service, and repository errors use literal strings, translation keys exist for each error, locale files contain [PACKAGE_NAME]-friendly messages, and [PACKAGE_NAME] Message constants are removed.

---

**Usage**: Replace `[PACKAGE_NAME]` with the specific package you're working on (e.g., `user`, `progression`, `financialdna`) and `[PackageName]` with the PascalCase version (e.g., `User`, `Progression`, `FinancialDna`), and `[package_name]` with the lowercase version for translation keys and messages.

**External**
Rewrite Brazilian Portuguese error message.

**Requirements**:
- All error messages must be friendly and appropriate for a gaming/educational app similar to Duolingo.
- In the json change only the text NOT the key.
- Create a English and Spanish version.
- DO NOT expose sensitive information to the user, like type of technology, third party names, etc.

JSON: